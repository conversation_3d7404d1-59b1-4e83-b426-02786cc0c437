'use client';

import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  Users,
  Award,
  MapPin,
  Calendar,
  Heart,
  Star,
  Target,
  Eye,
  Lightbulb,
  Shield,
  Globe,
  BookOpen,
  TrendingUp,
  Clock,
  CheckCircle
} from 'lucide-react'
import <PERSON><PERSON> from '@/components/ui/Button'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { TeamSection } from '@/components/about/TeamSection'
import { RotatingContentSection } from '@/components/about/RotatingContentSection'
import ScrollReveal from '@/components/animations/ScrollReveal'
import { StatCard } from '@/components/animations/AnimatedCounter'
import { TrustBadges } from '@/components/ui/SocialProof'

export default function AboutPage() {
  return (
    <>
      <Header />
      <main className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-coral-500/10 via-orange-500/5 to-teal-500/10" />
        <div className="max-w-7xl mx-auto px-4 relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <ScrollReveal direction="left" duration={0.8}>
              <div>
                <h1 className="text-5xl md:text-6xl font-display font-black text-gray-900 mb-6 leading-tight">
                  Transforming Education Through
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-coral-500 via-orange-500 to-teal-500"> Travel</span>
                </h1>
                <p className="text-xl text-gray-700 mb-8 leading-relaxed font-light">
                  Since 2009, Positive7 has been pioneering experiential learning through carefully crafted educational tours.
                  We believe that the world is the greatest classroom, and every journey is an opportunity to learn, grow, and discover.
                </p>
                <div className="flex flex-wrap gap-6">
                  <Link href="/contact">
                    <Button size="lg" className="btn-gradient shadow-xl hover:shadow-2xl">
                      Contact Us
                    </Button>
                  </Link>
                  <Link href="/trips">
                    <Button variant="outline" size="lg" className="border-2 border-coral-500 text-coral-600 hover:bg-coral-50">
                      View Our Trips
                    </Button>
                  </Link>
                </div>
              </div>
            </ScrollReveal>

            <ScrollReveal direction="right" duration={0.8} delay={0.2}>
              <div className="relative">
                <div className="relative h-96 rounded-3xl overflow-hidden shadow-2xl">
                  <Image
                    src="https://positive7.in/wp-content/uploads/2022/07/Manali-2.jpg"
                    alt="Students on educational tour"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                </div>

                {/* Floating Stats with Animation */}
                <div className="absolute -bottom-6 -left-6 backdrop-glass rounded-2xl p-6 text-white">
                  <StatCard
                    icon={<TrendingUp />}
                    value={15}
                    suffix="+"
                    label="Years of Excellence"
                    color="coral"
                    delay={0.5}
                  />
                </div>
                <div className="absolute -top-6 -right-6 backdrop-glass rounded-2xl p-6 text-white">
                  <StatCard
                    icon={<Users />}
                    value={1000}
                    suffix="+"
                    label="Students Impacted"
                    color="teal"
                    delay={0.7}
                  />
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-coral-400 rounded-full animate-float opacity-80" />
                <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-teal-400 rounded-full animate-float opacity-60" style={{ animationDelay: '2s' }} />
                <div className="absolute top-1/2 -right-8 w-4 h-4 bg-orange-400 rounded-full animate-float opacity-70" style={{ animationDelay: '1s' }} />
              </div>
            </ScrollReveal>
          </div>
        </div>
      </section>

      {/* Rotating Content Section */}
      <RotatingContentSection />

      {/* More Than Just Numbers Heading */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-green-50 rounded-2xl p-8">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                More Than Just Numbers
              </h3>
              <p className="text-gray-700 max-w-4xl mx-auto leading-relaxed">
                Behind every statistic is a story of transformation. From the shy student who discovered confidence on a mountain trek, 
                to the urban child who learned about rural life in a village homestay, to the group that bonded over a campfire under 
                the stars - these experiences create memories and learning that last a lifetime.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8 mt-8">
              <StatCard
                icon={<Shield />}
                value={0}
                label="Major Safety Incidents"
                description="in 15+ years of operations"
                color="teal"
                delay={0.2}
              />
              <StatCard
                icon={<CheckCircle />}
                value={100}
                suffix="%"
                label="Curriculum Aligned"
                description="programs designed with educators"
                color="coral"
                delay={0.4}
              />
              <StatCard
                icon={<Clock />}
                value={24}
                suffix="/7"
                label="Support Available"
                description="throughout every journey"
                color="orange"
                delay={0.6}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <TeamSection />

      {/* Why Choose Us */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-green-600">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose Positive7?</h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              We're not just a tour company - we're your partners in creating life-changing educational experiences
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: Shield,
                title: 'Safety First',
                description: 'Comprehensive safety protocols, trained guides, and 24/7 support ensure peace of mind for parents and educators.'
              },
              {
                icon: BookOpen,
                title: 'Educational Excellence',
                description: 'Every trip is designed with clear learning objectives, pre and post-trip activities, and curriculum alignment.'
              },
              {
                icon: Heart,
                title: 'Personal Growth',
                description: 'We focus on building confidence, independence, and life skills through carefully designed challenges and experiences.'
              },
              {
                icon: Globe,
                title: 'Cultural Immersion',
                description: 'Authentic local experiences that foster cultural understanding and global citizenship.'
              },
              {
                icon: Users,
                title: 'Expert Team',
                description: 'Experienced educators and travel professionals who understand both learning and adventure.'
              },
              {
                icon: Award,
                title: 'Proven Track Record',
                description: '15+ years of excellence with thousands of successful trips and satisfied schools.'
              }
            ].map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-blue-100 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Ready to Create Unforgettable Learning Experiences?
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            Join thousands of students who have transformed their learning through our educational tours
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-gradient-to-r from-blue-600 to-green-600">
                Plan Your Trip
              </Button>
            </Link>
            <Link href="/trips">
              <Button variant="outline" size="lg">
                Browse Destinations
              </Button>
            </Link>
          </div>
        </div>
      </section>
        </div>
      </main>
      <Footer />
    </>
  )
}
