'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Eye, EyeOff, Check, X, AlertCircle } from 'lucide-react';

interface InteractiveInputProps {
  label?: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'search';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  error?: string;
  success?: boolean;
  disabled?: boolean;
  required?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  className?: string;
  autoComplete?: string;
  maxLength?: number;
  pattern?: string;
  validation?: (value: string) => string | null;
  showCharCount?: boolean;
  animate?: boolean;
}

const InteractiveInput: React.FC<InteractiveInputProps> = ({
  label,
  type = 'text',
  placeholder,
  value = '',
  onChange,
  onFocus,
  onBlur,
  error,
  success = false,
  disabled = false,
  required = false,
  icon,
  iconPosition = 'left',
  className = '',
  autoComplete,
  maxLength,
  pattern,
  validation,
  showCharCount = false,
  animate = true,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [internalValue, setInternalValue] = useState(value);
  const [validationError, setValidationError] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
    
    // Run validation on blur
    if (validation && internalValue) {
      const validationResult = validation(internalValue);
      setValidationError(validationResult);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    onChange?.(newValue);
    
    // Clear validation error when user starts typing
    if (validationError) {
      setValidationError(null);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const inputType = type === 'password' && showPassword ? 'text' : type;
  const hasError = error || validationError;
  const hasValue = internalValue.length > 0;

  const getStatusIcon = () => {
    if (hasError) return <AlertCircle className="h-5 w-5 text-red-500" />;
    if (success) return <Check className="h-5 w-5 text-green-500" />;
    return null;
  };

  const getInputClasses = () => {
    const baseClasses = `
      w-full px-4 py-3 text-base bg-white dark:bg-gray-800 border rounded-xl
      transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
      placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-gray-100
      disabled:opacity-50 disabled:cursor-not-allowed
    `;

    if (hasError) {
      return `${baseClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`;
    }
    
    if (success) {
      return `${baseClasses} border-green-300 focus:border-green-500 focus:ring-green-500 dark:border-green-600`;
    }

    if (isFocused) {
      return `${baseClasses} border-coral-300 focus:border-coral-500 focus:ring-coral-500 dark:border-coral-600`;
    }

    return `${baseClasses} border-gray-300 hover:border-gray-400 dark:border-gray-600 dark:hover:border-gray-500`;
  };

  const getLabelClasses = () => {
    const baseClasses = `
      absolute left-4 transition-all duration-200 pointer-events-none
      text-gray-500 dark:text-gray-400
    `;

    if (isFocused || hasValue) {
      return `${baseClasses} -top-2 text-xs bg-white dark:bg-gray-800 px-1 text-coral-600 dark:text-coral-400`;
    }

    return `${baseClasses} top-3 text-base`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Floating label */}
      {label && (
        <motion.label
          className={getLabelClasses()}
          animate={animate ? {
            y: isFocused || hasValue ? -8 : 0,
            scale: isFocused || hasValue ? 0.85 : 1,
          } : {}}
          transition={{ duration: 0.2, ease: 'easeOut' }}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </motion.label>
      )}

      {/* Input container */}
      <div className="relative">
        {/* Left icon */}
        {icon && iconPosition === 'left' && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            {icon}
          </div>
        )}

        {/* Input field */}
        <motion.input
          ref={inputRef}
          type={inputType}
          value={internalValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={isFocused ? placeholder : ''}
          disabled={disabled}
          required={required}
          autoComplete={autoComplete}
          maxLength={maxLength}
          pattern={pattern}
          className={`${getInputClasses()} ${
            icon && iconPosition === 'left' ? 'pl-10' : ''
          } ${
            (type === 'password' || getStatusIcon() || (icon && iconPosition === 'right')) ? 'pr-10' : ''
          }`}
          animate={animate ? {
            scale: isFocused ? 1.02 : 1,
          } : {}}
          transition={{ duration: 0.2 }}
        />

        {/* Right icons */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
          {/* Status icon */}
          {getStatusIcon()}
          
          {/* Password toggle */}
          {type === 'password' && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          )}
          
          {/* Right icon */}
          {icon && iconPosition === 'right' && !type.includes('password') && !getStatusIcon() && (
            <div className="text-gray-400">
              {icon}
            </div>
          )}
        </div>
      </div>

      {/* Character count */}
      {showCharCount && maxLength && (
        <div className="flex justify-end mt-1">
          <span className={`text-xs ${
            internalValue.length > maxLength * 0.8 ? 'text-orange-500' : 'text-gray-400'
          }`}>
            {internalValue.length}/{maxLength}
          </span>
        </div>
      )}

      {/* Error message */}
      <AnimatePresence>
        {hasError && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-2"
          >
            <p className="text-sm text-red-600 dark:text-red-400 flex items-center space-x-1">
              <AlertCircle className="h-4 w-4" />
              <span>{error || validationError}</span>
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success message */}
      <AnimatePresence>
        {success && !hasError && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="mt-2"
          >
            <p className="text-sm text-green-600 dark:text-green-400 flex items-center space-x-1">
              <Check className="h-4 w-4" />
              <span>Looks good!</span>
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Focus ring effect */}
      {animate && isFocused && (
        <motion.div
          className="absolute inset-0 rounded-xl border-2 border-coral-500 pointer-events-none"
          initial={{ opacity: 0, scale: 1 }}
          animate={{ opacity: 0.3, scale: 1.02 }}
          exit={{ opacity: 0, scale: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </div>
  );
};

export default InteractiveInput;
