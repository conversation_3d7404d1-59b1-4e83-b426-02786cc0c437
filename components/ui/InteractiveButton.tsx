'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, Check, X, ArrowRight } from 'lucide-react';

interface InteractiveButtonProps {
  children: React.ReactNode;
  onClick?: () => void | Promise<void>;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  disabled?: boolean;
  loading?: boolean;
  success?: boolean;
  error?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  className?: string;
  ripple?: boolean;
  glow?: boolean;
  magnetic?: boolean;
}

const InteractiveButton: React.FC<InteractiveButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  success = false,
  error = false,
  icon,
  iconPosition = 'left',
  className = '',
  ripple = true,
  glow = false,
  magnetic = false,
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [ripples, setRipples] = useState<Array<{ id: number; x: number; y: number }>>([]);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  const handleClick = async (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // Create ripple effect
    if (ripple) {
      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const newRipple = { id: Date.now(), x, y };
      
      setRipples(prev => [...prev, newRipple]);
      
      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id));
      }, 600);
    }

    if (onClick) {
      await onClick();
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!magnetic) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;
    
    setMousePosition({ x: x * 0.1, y: y * 0.1 });
  };

  const handleMouseLeave = () => {
    if (magnetic) {
      setMousePosition({ x: 0, y: 0 });
    }
  };

  const getVariantClasses = () => {
    const baseClasses = 'relative overflow-hidden font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-gradient-to-r from-coral-500 to-orange-500 text-white hover:from-coral-600 hover:to-orange-600 focus:ring-coral-500 shadow-lg hover:shadow-xl dark:shadow-coral-500/25`;
      case 'secondary':
        return `${baseClasses} bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700`;
      case 'outline':
        return `${baseClasses} border-2 border-coral-500 text-coral-600 hover:bg-coral-50 focus:ring-coral-500 dark:text-coral-400 dark:border-coral-400 dark:hover:bg-coral-900/20`;
      case 'ghost':
        return `${baseClasses} text-gray-600 hover:bg-gray-100 focus:ring-gray-500 dark:text-gray-400 dark:hover:bg-gray-800`;
      default:
        return baseClasses;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm rounded-lg';
      case 'md':
        return 'px-4 py-2 text-base rounded-xl';
      case 'lg':
        return 'px-6 py-3 text-lg rounded-xl';
      case 'xl':
        return 'px-8 py-4 text-xl rounded-2xl';
      default:
        return 'px-4 py-2 text-base rounded-xl';
    }
  };

  const getStateIcon = () => {
    if (loading) return <Loader2 className="animate-spin" />;
    if (success) return <Check />;
    if (error) return <X />;
    if (icon) return icon;
    return null;
  };

  const iconSize = size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : size === 'xl' ? 'h-7 w-7' : 'h-5 w-5';

  return (
    <motion.button
      className={`${getVariantClasses()} ${getSizeClasses()} ${className} ${
        disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
      } ${glow ? 'animate-glow' : ''}`}
      onClick={handleClick}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      disabled={disabled || loading}
      animate={{
        x: mousePosition.x,
        y: mousePosition.y,
        scale: isPressed ? 0.98 : 1,
      }}
      whileHover={{ scale: magnetic ? 1.02 : 1.05 }}
      whileTap={{ scale: 0.95 }}
      transition={{ type: 'spring', stiffness: 400, damping: 25 }}
    >
      {/* Ripple effects */}
      <AnimatePresence>
        {ripples.map((ripple) => (
          <motion.span
            key={ripple.id}
            className="absolute bg-white/30 rounded-full pointer-events-none"
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: 4, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        ))}
      </AnimatePresence>

      {/* Button content */}
      <span className="relative flex items-center justify-center space-x-2">
        {iconPosition === 'left' && getStateIcon() && (
          <span className={iconSize}>{getStateIcon()}</span>
        )}
        
        <AnimatePresence mode="wait">
          <motion.span
            key={loading ? 'loading' : success ? 'success' : error ? 'error' : 'default'}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {children}
          </motion.span>
        </AnimatePresence>

        {iconPosition === 'right' && getStateIcon() && (
          <span className={iconSize}>{getStateIcon()}</span>
        )}
        
        {!getStateIcon() && iconPosition === 'right' && (
          <ArrowRight className={`${iconSize} transition-transform group-hover:translate-x-1`} />
        )}
      </span>

      {/* Glow effect */}
      {glow && (
        <motion.div
          className="absolute inset-0 rounded-inherit"
          animate={{
            boxShadow: [
              '0 0 20px rgba(251, 146, 60, 0.4)',
              '0 0 40px rgba(251, 146, 60, 0.6)',
              '0 0 20px rgba(251, 146, 60, 0.4)',
            ],
          }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      )}
    </motion.button>
  );
};

export default InteractiveButton;
