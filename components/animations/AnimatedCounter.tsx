'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useInView, useMotionValue, useSpring } from 'framer-motion';

interface AnimatedCounterProps {
  from?: number;
  to: number;
  duration?: number;
  delay?: number;
  suffix?: string;
  prefix?: string;
  className?: string;
  decimals?: number;
  separator?: string;
}

const AnimatedCounter: React.FC<AnimatedCounterProps> = ({
  from = 0,
  to,
  duration = 2,
  delay = 0,
  suffix = '',
  prefix = '',
  className = '',
  decimals = 0,
  separator = ',',
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: '-10%' });
  const [hasAnimated, setHasAnimated] = useState(false);
  
  const motionValue = useMotionValue(from);
  const springValue = useSpring(motionValue, {
    duration: duration * 1000,
    bounce: 0,
  });
  
  const [displayValue, setDisplayValue] = useState(from);

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true);
      setTimeout(() => {
        motionValue.set(to);
      }, delay * 1000);
    }
  }, [isInView, hasAnimated, motionValue, to, delay]);

  useEffect(() => {
    const unsubscribe = springValue.on('change', (latest) => {
      setDisplayValue(latest);
    });
    return unsubscribe;
  }, [springValue]);

  const formatNumber = (num: number) => {
    const rounded = Number(num.toFixed(decimals));
    const parts = rounded.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    return parts.join('.');
  };

  return (
    <motion.span
      ref={ref}
      className={className}
      initial={{ opacity: 0, scale: 0.5 }}
      animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.5 }}
      transition={{ duration: 0.5, delay: delay }}
    >
      {prefix}{formatNumber(displayValue)}{suffix}
    </motion.span>
  );
};

export default AnimatedCounter;

// Statistics card component with animated counter
interface StatCardProps {
  icon: React.ReactNode;
  value: number;
  suffix?: string;
  prefix?: string;
  label: string;
  description?: string;
  color?: 'blue' | 'green' | 'purple' | 'coral' | 'teal' | 'orange';
  delay?: number;
}

export const StatCard: React.FC<StatCardProps> = ({
  icon,
  value,
  suffix = '',
  prefix = '',
  label,
  description,
  color = 'blue',
  delay = 0,
}) => {
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 text-blue-600',
    green: 'from-green-500 to-green-600 text-green-600',
    purple: 'from-purple-500 to-purple-600 text-purple-600',
    coral: 'from-coral-500 to-coral-600 text-coral-600',
    teal: 'from-teal-500 to-teal-600 text-teal-600',
    orange: 'from-orange-500 to-orange-600 text-orange-600',
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: '-10%' }}
      transition={{ duration: 0.6, delay }}
      className="text-center group"
    >
      <motion.div
        whileHover={{ scale: 1.05, rotate: 5 }}
        transition={{ type: 'spring', stiffness: 300 }}
        className={`w-16 h-16 bg-gradient-to-r ${colorClasses[color].split(' ').slice(0, 2).join(' ')} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
      >
        <div className="text-white text-2xl">
          {icon}
        </div>
      </motion.div>
      
      <div className={`text-4xl font-bold ${colorClasses[color].split(' ')[2]} mb-2`}>
        <AnimatedCounter
          to={value}
          suffix={suffix}
          prefix={prefix}
          delay={delay + 0.3}
          duration={2.5}
        />
      </div>
      
      <div className="text-gray-700 font-semibold text-lg mb-1">{label}</div>
      
      {description && (
        <div className="text-sm text-gray-600">{description}</div>
      )}
    </motion.div>
  );
};

// Progress bar with animation
interface AnimatedProgressProps {
  value: number;
  max?: number;
  label?: string;
  color?: string;
  height?: string;
  delay?: number;
  duration?: number;
}

export const AnimatedProgress: React.FC<AnimatedProgressProps> = ({
  value,
  max = 100,
  label,
  color = 'bg-gradient-to-r from-coral-500 to-orange-500',
  height = 'h-3',
  delay = 0,
  duration = 1.5,
}) => {
  const percentage = Math.min((value / max) * 100, 100);

  return (
    <div className="w-full">
      {label && (
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          <span className="text-sm text-gray-500">
            <AnimatedCounter to={value} suffix={`/${max}`} delay={delay} />
          </span>
        </div>
      )}
      
      <div className={`w-full bg-gray-200 rounded-full ${height} overflow-hidden`}>
        <motion.div
          initial={{ width: 0 }}
          whileInView={{ width: `${percentage}%` }}
          viewport={{ once: true }}
          transition={{ duration, delay, ease: 'easeOut' }}
          className={`${height} ${color} rounded-full relative overflow-hidden`}
        >
          <motion.div
            animate={{ x: ['0%', '100%'] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
            className="absolute inset-0 bg-white/20 w-full"
            style={{
              background: 'linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%)',
            }}
          />
        </motion.div>
      </div>
    </div>
  );
};
