'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronLeft,
  ChevronRight,
  Play,
  MapPin,
  Calendar,
  Users,
  Star,
  ArrowRight
} from 'lucide-react';
import { COMPANY_INFO } from '@/lib/constants';
import { cn } from '@/lib/utils';
import type { Trip } from '@/types/database';

interface HeroTrip {
  id: string;
  title: string;
  slug: string;
  description: string | null;
  destination: string;
  featured_image_url: string | null;
}

interface FallbackSlide {
  id: string;
  image_url: string;
}

interface HeroSectionProps {
  heroTrips: HeroTrip[];
}

// Fallback slides if no trips are available
const fallbackSlides: FallbackSlide[] = [
  {
    id: 'fallback-1',
    image_url: '/images/trips/gettyimages-1134041601-612x612-1.jpg',
  },
  {
    id: 'fallback-2',
    image_url: '/images/trips/dusk-time-rishikesh-holy-town-travel-destination-india-1024x684.jpg',
  },
  {
    id: 'fallback-3',
    image_url: '/images/fallback-hero.jpg',
  },
];

export default function HeroSection({ heroTrips }: HeroSectionProps) {
  const isFallbackMode = heroTrips.length === 0;
  // Use provided trips or fallback slides
  const slides = isFallbackMode ? fallbackSlides : heroTrips;

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [scrollY, setScrollY] = useState(0);

  // Parallax effect
  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isPlaying, slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-600/20 via-coral-500/10 to-teal-600/20 animate-gradient-xy" />

      {/* Background Slides with Parallax */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.8, ease: "easeInOut" }}
          className="absolute inset-0"
          style={{
            transform: `translateY(${scrollY * 0.5}px)`,
          }}
        >
          <Image
            src={isFallbackMode
              ? (slides[currentSlide] as FallbackSlide).image_url
              : (slides[currentSlide] as HeroTrip).featured_image_url || '/images/fallback-hero.jpg'
            }
            alt={isFallbackMode ? 'Positive7 - Bring Learning to Life' : (slides[currentSlide] as HeroTrip).title}
            fill
            className="object-cover transition-transform duration-[2000ms] ease-out"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-primary-900/30 via-transparent to-coral-900/30" />
        </motion.div>
      </AnimatePresence>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-2 h-2 bg-secondary-400 rounded-full animate-float opacity-60" />
        <div className="absolute top-40 right-20 w-3 h-3 bg-coral-400 rounded-full animate-float opacity-40" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-40 left-20 w-1 h-1 bg-teal-400 rounded-full animate-float opacity-80" style={{ animationDelay: '4s' }} />
        <div className="absolute top-60 left-1/3 w-2 h-2 bg-orange-400 rounded-full animate-float opacity-50" style={{ animationDelay: '1s' }} />
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="container-custom">
          <div className="max-w-4xl">
            <AnimatePresence mode="wait">
              {isFallbackMode ? (
                <motion.div
                  key="fallback-content"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-white text-center mx-auto"
                >
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    className="mb-8"
                  >
                    <motion.h1
                      className="text-6xl md:text-8xl font-display font-black text-white mb-6 bg-gradient-to-r from-white via-secondary-200 to-coral-200 bg-clip-text text-transparent"
                      initial={{ opacity: 0, y: 30, scale: 0.9 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ duration: 0.8, delay: 0.5, ease: "easeOut" }}
                    >
                      Positive7
                    </motion.h1>
                    <motion.div
                      initial={{ width: 0, opacity: 0 }}
                      animate={{ width: "240px", opacity: 1 }}
                      transition={{ duration: 1, delay: 0.8 }}
                      className="h-1.5 bg-gradient-to-r from-secondary-400 via-coral-400 to-teal-400 rounded-full mx-auto mb-8 shadow-lg shadow-secondary-400/50"
                    ></motion.div>
                    <motion.p
                      className="text-3xl md:text-6xl font-display font-light text-white tracking-wide leading-tight"
                      initial={{ opacity: 0, y: 30 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.8, delay: 1, ease: "easeOut" }}
                    >
                      <span className="bg-gradient-to-r from-secondary-300 to-coral-300 bg-clip-text text-transparent">
                        Bring Learning to Life
                      </span>
                    </motion.p>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.4 }}
                    className="flex flex-col sm:flex-row gap-6 justify-center mt-12"
                  >
                    <Link
                      href="/trips"
                      className="group relative inline-flex items-center px-10 py-5 bg-gradient-to-r from-coral-500 to-orange-500 text-white font-bold rounded-2xl hover:from-coral-600 hover:to-orange-600 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-coral-500/25 overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-shimmer-gradient animate-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <span className="relative z-10">View Our Trips</span>
                      <ArrowRight className="relative z-10 ml-3 h-5 w-5 group-hover:translate-x-2 transition-transform duration-300" />
                    </Link>

                    <Link
                      href="/contact"
                      className="group inline-flex items-center px-10 py-5 border-2 border-white/80 text-white font-bold rounded-2xl hover:bg-white/10 backdrop-blur-sm transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-white/10 hover:border-white"
                    >
                      <span className="group-hover:text-secondary-200 transition-colors duration-300">Contact Us</span>
                    </Link>
                  </motion.div>
                </motion.div>
              ) : (
                <motion.div
                  key={currentSlide}
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="text-white"
                >
                  {/* Company Quote */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="mb-6"
                  >
                    <p className="text-lg md:text-xl font-medium text-secondary-300 mb-2">
                      {COMPANY_INFO.heroQuote}
                    </p>
                    <div className="w-20 h-1 bg-secondary-400 rounded"></div>
                  </motion.div>

                  {/* Main Content */}
                  <motion.h1
                    initial={{ opacity: 0, y: 40, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
                    className="text-5xl md:text-7xl lg:text-8xl font-display font-black text-white mb-6 leading-tight"
                  >
                    <span className="bg-gradient-to-r from-white via-secondary-200 to-coral-200 bg-clip-text text-transparent">
                      {(slides[currentSlide] as HeroTrip).title}
                    </span>
                  </motion.h1>

                  <motion.h2
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
                    className="text-2xl md:text-4xl lg:text-5xl font-heading font-semibold mb-6"
                  >
                    <span className="bg-gradient-to-r from-secondary-300 to-teal-300 bg-clip-text text-transparent">
                      {(slides[currentSlide] as HeroTrip).destination}
                    </span>
                  </motion.h2>

                  <motion.p
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
                    className="text-xl md:text-2xl text-gray-100 mb-10 max-w-3xl leading-relaxed font-light"
                  >
                    {(slides[currentSlide] as HeroTrip).description || 'Discover amazing educational experiences with Positive7.'}
                  </motion.p>

                  {/* CTA Buttons */}
                  <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.0 }}
                    className="flex flex-col sm:flex-row gap-6"
                  >
                    <Link
                      href={`/trips/${(slides[currentSlide] as HeroTrip).slug}`}
                      className="group relative inline-flex items-center px-10 py-5 bg-gradient-to-r from-primary-600 to-teal-600 text-white font-bold rounded-2xl hover:from-primary-700 hover:to-teal-700 transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-primary-500/25 overflow-hidden"
                    >
                      <div className="absolute inset-0 bg-shimmer-gradient animate-shimmer opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      <span className="relative z-10">Explore {(slides[currentSlide] as HeroTrip).destination}</span>
                      <ArrowRight className="relative z-10 ml-3 h-5 w-5 group-hover:translate-x-2 transition-transform duration-300" />
                    </Link>

                    <Link
                      href="/trips"
                      className="group inline-flex items-center px-10 py-5 border-2 border-white/80 text-white font-bold rounded-2xl hover:bg-white/10 backdrop-blur-sm transition-all duration-500 transform hover:scale-105 hover:shadow-2xl hover:shadow-white/10 hover:border-white"
                    >
                      <span className="group-hover:text-secondary-200 transition-colors duration-300">View All Trips</span>
                    </Link>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
      >
        <div className="flex items-center space-x-6 bg-black/20 backdrop-blur-md rounded-2xl px-6 py-4 border border-white/20">
          {/* Slide Indicators */}
          <div className="flex space-x-3">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  'w-4 h-4 rounded-full transition-all duration-500 hover:scale-110',
                  currentSlide === index
                    ? 'bg-gradient-to-r from-coral-400 to-orange-400 scale-125 shadow-lg shadow-coral-400/50'
                    : 'bg-white/40 hover:bg-white/60 backdrop-blur-sm'
                )}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Play/Pause Button */}
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="p-3 bg-white/10 backdrop-blur-sm rounded-xl hover:bg-white/20 transition-all duration-300 hover:scale-110 border border-white/20"
            aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
          >
            <Play className={cn('h-5 w-5 text-white transition-opacity duration-300', isPlaying && 'opacity-60')} />
          </button>
        </div>
      </motion.div>

      {/* Arrow Navigation */}
      <motion.button
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 1.2 }}
        onClick={prevSlide}
        className="absolute left-6 top-1/2 transform -translate-y-1/2 z-20 p-4 bg-black/20 backdrop-blur-md rounded-2xl hover:bg-black/30 transition-all duration-300 group border border-white/20 hover:border-white/40 hover:scale-110"
        aria-label="Previous slide"
      >
        <ChevronLeft className="h-7 w-7 text-white group-hover:scale-110 transition-transform duration-300" />
      </motion.button>

      <motion.button
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, delay: 1.2 }}
        onClick={nextSlide}
        className="absolute right-6 top-1/2 transform -translate-y-1/2 z-20 p-4 bg-black/20 backdrop-blur-md rounded-2xl hover:bg-black/30 transition-all duration-300 group border border-white/20 hover:border-white/40 hover:scale-110"
        aria-label="Next slide"
      >
        <ChevronRight className="h-7 w-7 text-white group-hover:scale-110 transition-transform duration-300" />
      </motion.button>

      {/* Quick Stats - Only shown in non-fallback mode */}
      {!isFallbackMode && (
        <motion.div
          initial={{ opacity: 0, y: 50, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, delay: 1.3 }}
          className="absolute bottom-24 right-8 hidden lg:block z-20"
        >
          <div className="bg-black/20 backdrop-blur-xl rounded-2xl p-8 text-white border border-white/20 shadow-2xl shadow-black/20">
            <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-secondary-300 to-coral-300 bg-clip-text text-transparent">
              Why Choose Positive7?
            </h3>
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 1.5 }}
                className="flex items-center space-x-4"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Users className="h-5 w-5" />
                </div>
                <span className="text-base font-medium">1000+ Happy Students</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 1.7 }}
                className="flex items-center space-x-4"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-coral-500 to-orange-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Star className="h-5 w-5" />
                </div>
                <span className="text-base font-medium">5-Star Rated Experiences</span>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 1.9 }}
                className="flex items-center space-x-4"
              >
                <div className="w-10 h-10 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-xl flex items-center justify-center shadow-lg">
                  <MapPin className="h-5 w-5" />
                </div>
                <span className="text-base font-medium">Unique Educational Destinations</span>
              </motion.div>
            </div>
          </div>
        </motion.div>
      )}
    </section>
  );
}
